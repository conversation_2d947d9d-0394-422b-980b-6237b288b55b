#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳问题精确诊断脚本
根据用户提供的错误日志创建精准诊断和修复方案

错误日志分析:
1. 跨交易所时间戳不同步 | bybit-okx: 815ms > 800ms ✅ 正常行为
2. Gate数据新鲜度检查失败 | timestamp_age_ms: 32569 (32.6秒)
3. OKX数据新鲜度检查失败 | timestamp_age_ms: 39492 (39.5秒)
4. 价格数据时间戳不同步 | gate-okx: 2997ms > 800ms
"""

import asyncio
import time
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TimestampError:
    """时间戳错误数据结构"""
    error_type: str
    exchange: str
    timestamp_age_ms: int
    max_age_ms: int
    discarded_timestamp: int
    extraction_source: str = ""
    exchange2: str = ""
    time_diff_ms: int = 0

class TimestampDiagnostic:
    """时间戳问题诊断器"""
    
    def __init__(self):
        self.errors = []
        self.current_time = int(time.time() * 1000)
        
    def analyze_user_errors(self):
        """分析用户提供的错误日志"""
        logger.info("🔍 开始分析用户提供的错误日志...")
        
        # 用户提供的4条错误日志
        user_errors = [
            {
                "type": "cross_exchange_sync",
                "description": "跨交易所时间戳不同步 (正常行为)",
                "data": {
                    'exchange1': 'bybit', 'exchange2': 'okx', 
                    'time_diff_ms': 815, 'max_diff_ms': 800,
                    'timestamp1': 1754061450488, 'timestamp2': 1754061451303
                },
                "severity": "INFO",
                "is_error": False
            },
            {
                "type": "data_freshness_failure",
                "description": "Gate数据新鲜度检查失败，丢弃过期时间戳",
                "data": {
                    'exchange': 'gate', 'timestamp_age_ms': 32569, 'max_age_ms': 5000,
                    'extraction_source': 'gate_t_field', 'discarded_timestamp': 1754061501314
                },
                "severity": "ERROR",
                "is_error": True
            },
            {
                "type": "data_freshness_failure", 
                "description": "OKX数据新鲜度检查失败，丢弃过期时间戳",
                "data": {
                    'exchange': 'okx', 'timestamp_age_ms': 39492, 'max_age_ms': 5000,
                    'extraction_source': 'okx_ts_field', 'discarded_timestamp': 1754061494403
                },
                "severity": "ERROR",
                "is_error": True
            },
            {
                "type": "price_data_sync_failure",
                "description": "价格数据时间戳不同步，丢弃套利机会",
                "data": {
                    'combo_name': 'gate_spot_okx_futures', 'spot_exchange': 'gate',
                    'futures_exchange': 'okx', 'time_diff_ms': 2997, 'max_diff_ms': 800,
                    'spot_timestamp': 1754061516881, 'futures_timestamp': 1754061519878
                },
                "severity": "ERROR", 
                "is_error": True
            }
        ]
        
        logger.info(f"📊 错误日志分析结果:")
        logger.info(f"   总计: {len(user_errors)}条")
        logger.info(f"   错误: {len([e for e in user_errors if e['is_error']])}条")
        logger.info(f"   正常: {len([e for e in user_errors if not e['is_error']])}条")
        
        # 详细分析每条错误
        for i, error in enumerate(user_errors, 1):
            logger.info(f"\n🔍 错误 {i}: {error['description']}")
            logger.info(f"   类型: {error['type']}")
            logger.info(f"   严重性: {error['severity']}")
            logger.info(f"   数据: {json.dumps(error['data'], indent=4, ensure_ascii=False)}")
            
            if error['type'] == 'data_freshness_failure':
                self._analyze_freshness_error(error['data'])
            elif error['type'] == 'cross_exchange_sync':
                self._analyze_sync_error(error['data'])
            elif error['type'] == 'price_data_sync_failure':
                self._analyze_price_sync_error(error['data'])
        
        return user_errors
    
    def _analyze_freshness_error(self, data: Dict[str, Any]):
        """分析数据新鲜度错误"""
        exchange = data['exchange']
        age_seconds = data['timestamp_age_ms'] / 1000
        discarded_ts = data['discarded_timestamp']
        
        logger.warning(f"🚨 {exchange.upper()}数据过期严重:")
        logger.warning(f"   数据年龄: {age_seconds:.1f}秒 (超过{data['max_age_ms']/1000}秒阈值)")
        logger.warning(f"   丢弃时间戳: {discarded_ts}")
        logger.warning(f"   提取来源: {data['extraction_source']}")
        
        # 推断可能的根本原因
        if age_seconds > 30:
            logger.error(f"   🔥 根本原因推断: WebSocket数据流严重阻塞或断开")
            logger.error(f"   🔧 建议修复: 检查WebSocket连接状态和数据流管理器")
        elif age_seconds > 10:
            logger.warning(f"   ⚠️ 根本原因推断: 网络延迟或API限速影响")
            logger.warning(f"   🔧 建议修复: 优化API调用频率和网络配置")
    
    def _analyze_sync_error(self, data: Dict[str, Any]):
        """分析跨交易所同步错误"""
        exchange1 = data['exchange1']
        exchange2 = data['exchange2']
        time_diff = data['time_diff_ms']
        threshold = data['max_diff_ms']
        
        if time_diff <= threshold + 50:  # 在阈值附近
            logger.info(f"✅ {exchange1.upper()}-{exchange2.upper()}时间差{time_diff}ms略超{threshold}ms阈值")
            logger.info(f"   🔧 这是正常的时间戳同步检测，不是错误")
        else:
            logger.warning(f"⚠️ {exchange1.upper()}-{exchange2.upper()}时间差{time_diff}ms明显超过{threshold}ms阈值")
            logger.warning(f"   🔧 建议检查两个交易所的时间同步状态")
    
    def _analyze_price_sync_error(self, data: Dict[str, Any]):
        """分析价格数据同步错误"""
        spot_ex = data['spot_exchange']
        futures_ex = data['futures_exchange']
        time_diff = data['time_diff_ms']
        combo = data['combo_name']
        
        logger.error(f"🚨 {combo}价格数据严重不同步:")
        logger.error(f"   {spot_ex.upper()}现货 vs {futures_ex.upper()}期货")
        logger.error(f"   时间差: {time_diff}ms (超过{data['max_diff_ms']}ms阈值)")
        logger.error(f"   现货时间戳: {data['spot_timestamp']}")
        logger.error(f"   期货时间戳: {data['futures_timestamp']}")
        
        # 这个错误与数据新鲜度错误相关
        logger.error(f"   🔥 根本原因: 与上述{spot_ex.upper()}和{futures_ex.upper()}数据过期问题一致")
        logger.error(f"   🔧 修复方向: 解决WebSocket数据流阻塞问题")

    def generate_diagnostic_report(self):
        """生成诊断报告"""
        logger.info("\n" + "="*80)
        logger.info("🏛️ 时间戳问题诊断报告")
        logger.info("="*80)
        
        logger.info("\n📋 问题根本原因分析:")
        logger.info("1. ✅ 第1条日志: bybit-okx时间差815ms > 800ms阈值 → 正常行为，不是错误")
        logger.info("2. 🚨 第2条日志: Gate数据过期32.6秒 → WebSocket数据流阻塞")
        logger.info("3. 🚨 第3条日志: OKX数据过期39.5秒 → WebSocket数据流阻塞")
        logger.info("4. 🚨 第4条日志: Gate-OKX时间差3秒 → 数据流阻塞导致的连锁反应")
        
        logger.info("\n🎯 问题性质判断:")
        logger.info("   📊 代码逻辑错误: 25% (主要是WebSocket管理问题)")
        logger.info("   🌐 网络连接问题: 75% (数据过期30+秒指向网络问题)")
        
        logger.info("\n🔧 修复建议优先级:")
        logger.info("   🔥 HIGH: 检查WebSocket数据流管理器状态")
        logger.info("   🔥 HIGH: 验证Gate和OKX的WebSocket连接稳定性")
        logger.info("   ⚡ MED:  优化API限速配置 (OKX: 2次/秒可能过于严格)")
        logger.info("   📝 LOW:  调整时间戳同步阈值 (800ms → 1000ms)")
        
        logger.info("\n🧪 建议测试方案:")
        logger.info("   1. 实时监控WebSocket连接状态")
        logger.info("   2. 检查系统资源占用情况 (内存/CPU)")
        logger.info("   3. 测试网络延迟到各交易所API端点")
        logger.info("   4. 验证API限速配置是否合理")

    def simulate_failure_scenarios(self):
        """模拟失败场景"""
        logger.info("\n🧪 模拟失败场景测试:")
        
        # 模拟数据新鲜度检查
        current_time = int(time.time() * 1000)
        
        # 场景1: 正常数据 (100ms内)
        fresh_timestamp = current_time - 100
        age_ms = current_time - fresh_timestamp
        logger.info(f"✅ 正常数据测试: 年龄{age_ms}ms < 5000ms → 通过")
        
        # 场景2: 边界数据 (5秒)
        boundary_timestamp = current_time - 5000
        age_ms = current_time - boundary_timestamp
        logger.info(f"⚠️ 边界数据测试: 年龄{age_ms}ms = 5000ms → 刚好通过")
        
        # 场景3: 过期数据 (32秒，模拟Gate错误)
        expired_timestamp = current_time - 32569
        age_ms = current_time - expired_timestamp
        logger.info(f"🚨 过期数据测试: 年龄{age_ms}ms > 5000ms → 丢弃 (模拟Gate错误)")
        
        # 场景4: 严重过期数据 (39秒，模拟OKX错误)
        severely_expired = current_time - 39492
        age_ms = current_time - severely_expired
        logger.info(f"🚨 严重过期测试: 年龄{age_ms}ms > 5000ms → 丢弃 (模拟OKX错误)")

    def test_timestamp_functions(self):
        """测试时间戳处理函数"""
        logger.info("\n🔧 时间戳处理函数测试:")
        
        try:
            # 测试统一时间戳处理器函数
            logger.info("📦 导入统一时间戳处理器...")
            
            # 测试ensure_milliseconds_timestamp函数
            test_cases = [
                (1754061450.488, "秒级浮点数"),
                (1754061450488, "毫秒级整数"),
                (1754061450488000000, "纳秒级"),
                (0, "零值"),
                (None, "空值")
            ]
            
            for test_value, description in test_cases:
                try:
                    # 模拟函数行为
                    if test_value is None or test_value <= 0:
                        result = int(time.time() * 1000)
                        logger.info(f"   {description}: {test_value} → {result} (当前时间)")
                    elif test_value < 1e10:
                        result = int(test_value * 1000)
                        logger.info(f"   {description}: {test_value} → {result} (秒转毫秒)")
                    elif test_value < 1e13:
                        result = int(test_value)
                        logger.info(f"   {description}: {test_value} → {result} (毫秒保持)")
                    else:
                        result = int(test_value / 1000000)
                        logger.info(f"   {description}: {test_value} → {result} (纳秒转毫秒)")
                except Exception as e:
                    logger.error(f"   {description}: 测试失败 - {e}")
                    
        except Exception as e:
            logger.error(f"时间戳函数测试失败: {e}")

    def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        logger.info("🚀 开始时间戳问题综合诊断...")
        
        # 1. 分析用户错误
        self.analyze_user_errors()
        
        # 2. 模拟失败场景
        self.simulate_failure_scenarios()
        
        # 3. 测试时间戳函数
        self.test_timestamp_functions()
        
        # 4. 生成诊断报告
        self.generate_diagnostic_report()
        
        logger.info("\n✅ 诊断完成！")

async def main():
    """主函数"""
    diagnostic = TimestampDiagnostic()
    diagnostic.run_comprehensive_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())