2025-08-01 17:15:43 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-01 17:15:43 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-01 17:15:43 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-01 17:15:43 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-01 17:15:43.329 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-01 17:15:43.329 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-01 17:15:43 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-01 17:15:43 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-01 17:15:43 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-01 17:15:43 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-01 17:15:43.785 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:15:44.104 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:15:44.104 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:15:44.448 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:15:44.448 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:15:44 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-01 17:15:44.449 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:15:44.775 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:15:44.776 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:15:45.097 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:15:45.097 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:15:45 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-01 17:15:45.097 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:15:45.453 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:15:45.453 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:15:45.756 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:15:45.756 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:15:45 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-01 17:15:45.756 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:15:46.081 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:15:46.081 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:15:46.415 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:15:46.415 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:15:46 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-01 17:15:46 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-01 17:15:46.741 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 17:15:48.113 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 17:15:49.609 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:15:50.277 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:15:50.856 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:15:51.191 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:15:51.760 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:15:52.324 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:16:12.177 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:12.178 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:12.833 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:12.834 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:13.498 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:13.498 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:14.155 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:14.155 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:14.806 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:14.806 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:15.474 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:16:15.474 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:16:57.668 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 17:16:57.984 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 17:16:58.010 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:16:58.011 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-01 17:16:58.020 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:00.415 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-01 17:17:00.760 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-01 17:17:00.763 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-01 17:17:00.773 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-01 17:17:00.782 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-01 17:17:01.784 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.118 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.119 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.119 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.119 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.119 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.119 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.120 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.120 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.120 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-01 17:17:02.209 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:02.210 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:02.588 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:02.588 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:02.589 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:02.589 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:02.589 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:02.589 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:02.590 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:02.590 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:02.648 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 17:17:02.648 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 17:17:02.649 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 17:17:02 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 17:17:02.677 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 17:17:02.677 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 17:17:02.678 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 17:17:02 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 17:17:02.680 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:02.680 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:02.680 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 17:17:02.681 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 17:17:02.682 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 17:17:02 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 17:17:02.682 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 17:17:02.682 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 17:17:02.682 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 17:17:02.683 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 17:17:02.683 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 17:17:02.683 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 17:17:02.684 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 17:17:02.684 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 17:17:02 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 17:17:02.696 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-01 17:17:02.698 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-01 17:17:02.698 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-01 17:17:02.698 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-01 17:17:02.698 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-01 17:17:02.698 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-01 17:17:02.699 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-01 17:17:02.700 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-01 17:17:02 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-01 17:17:02.856 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:02.856 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:02.870 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:02.870 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:02.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:02.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:02.927 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:02.927 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:04.727 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:04.727 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:05.054 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:05.055 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:05.059 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:05.059 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:05.064 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:05.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:05.066 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:05.066 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:05.092 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-01 17:17:05.092 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-01 17:17:05.388 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:05.388 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:05.400 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:05.400 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:05.401 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:05.401 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:05.408 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-01 17:17:05.409 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-01 17:17:13.906 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 17:17:15.396 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 17:17:16.916 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:17.569 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:18.159 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:18.466 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:17:19.047 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:17:19.632 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:17:24.525 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-01 17:17:25.993 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-01 17:17:27.494 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:28.166 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:28.763 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-01 17:17:29.078 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:17:29.685 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-01 17:17:30.267 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
