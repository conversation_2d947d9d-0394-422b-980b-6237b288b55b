#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳精确诊断脚本
专门诊断时间戳ms过高问题 - 是代码错误还是VPS网络问题

根据用户反馈的9000多ms时间戳差异问题进行深度分析
"""

import sys
import os
import time
import json
import asyncio
import statistics
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
project_root = '/root/myproject/123/66B 修复时间戳问题，但是全是错误/123'
sys.path.insert(0, project_root)

try:
    # 先尝试直接导入模块文件
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "unified_timestamp_processor", 
        f"{project_root}/websocket/unified_timestamp_processor.py"
    )
    unified_timestamp_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(unified_timestamp_module)
    
    # 获取所需函数
    ensure_milliseconds_timestamp = unified_timestamp_module.ensure_milliseconds_timestamp
    calculate_data_age = unified_timestamp_module.calculate_data_age
    get_synced_timestamp = unified_timestamp_module.get_synced_timestamp
    get_timestamp_processor = unified_timestamp_module.get_timestamp_processor
    
    print("✅ 成功导入统一时间戳处理器")
except Exception as e:
    print(f"❌ 导入统一时间戳处理器失败: {e}")
    # 提供基础实现作为兜底
    def ensure_milliseconds_timestamp(timestamp):
        import time
        if timestamp is None or timestamp <= 0:
            return int(time.time() * 1000)
        if timestamp < 1e12:
            return int(timestamp * 1000)
        return int(timestamp)
    
    def calculate_data_age(data_timestamp, current_time=None):
        import time
        if current_time is None:
            current_time = time.time()
        data_ts_ms = ensure_milliseconds_timestamp(data_timestamp)
        current_ts_ms = int(current_time * 1000)
        return abs(current_ts_ms - data_ts_ms) / 1000.0
    
    def get_synced_timestamp(exchange, data=None):
        import time
        return int(time.time() * 1000)
    
    def get_timestamp_processor(exchange):
        # 简化的处理器类
        class SimpleProcessor:
            def validate_cross_exchange_sync(self, ts1, ts2, ex1, ex2, max_diff_ms=800):
                diff = abs(ts1 - ts2)
                return diff <= max_diff_ms, diff
        return SimpleProcessor()
    
    print("⚠️ 使用兜底实现继续诊断")

class TimestampDiagnosticTool:
    """时间戳精确诊断工具"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "diagnostic_type": "timestamp_precision_analysis",
            "findings": [],
            "network_tests": [],
            "code_tests": [],
            "timestamp_samples": {},
            "conclusion": "",
            "recommendations": []
        }
        self.current_time_ms = int(time.time() * 1000)
        
    def log_finding(self, level: str, message: str, data: Optional[Dict] = None):
        """记录诊断发现"""
        finding = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            "data": data or {}
        }
        self.results["findings"].append(finding)
        
        # 实时输出
        emoji = {"CRITICAL": "🚨", "HIGH": "⚠️", "MEDIUM": "🔍", "INFO": "ℹ️"}
        print(f"{emoji.get(level, 'ℹ️')} [{level}] {message}")
        if data:
            print(f"   数据: {data}")
    
    def test_timestamp_functions(self):
        """测试时间戳处理函数的准确性"""
        print("\n🔧 开始测试时间戳处理函数...")
        
        test_cases = [
            # (输入时间戳, 预期输出类型, 描述)
            (1754058178612, "int", "标准毫秒级时间戳"),
            (1754058178.612, "int", "浮点数毫秒级时间戳"), 
            (1754058178, "int", "秒级时间戳"),
            (1754058178.0, "int", "浮点数秒级时间戳"),
            (None, "int", "None值处理"),
            (0, "int", "零值处理"),
            (-1, "int", "负值处理")
        ]
        
        for timestamp_input, expected_type, description in test_cases:
            try:
                result = ensure_milliseconds_timestamp(timestamp_input)
                success = isinstance(result, int) and result > 0
                
                self.log_finding(
                    "INFO" if success else "HIGH",
                    f"时间戳标准化测试: {description}",
                    {
                        "input": timestamp_input,
                        "output": result,
                        "output_type": type(result).__name__,
                        "expected_type": expected_type,
                        "success": success
                    }
                )
                
                self.results["code_tests"].append({
                    "test": "ensure_milliseconds_timestamp",
                    "input": timestamp_input,
                    "output": result,
                    "success": success,
                    "description": description
                })
                
            except Exception as e:
                self.log_finding("CRITICAL", f"时间戳标准化异常: {description}", {"error": str(e)})
    
    def test_data_age_calculation(self):
        """测试数据年龄计算的准确性"""
        print("\n🔧 开始测试数据年龄计算...")
        
        current_time = time.time()
        
        test_cases = [
            # (数据时间戳, 当前时间, 预期年龄秒数, 描述)
            (int((current_time - 1) * 1000), current_time, 1.0, "1秒前的毫秒级时间戳"),
            (int((current_time - 0.5) * 1000), current_time, 0.5, "500ms前的毫秒级时间戳"),
            (int(current_time - 1), current_time, 1.0, "1秒前的秒级时间戳"),
            (int(current_time - 10), current_time, 10.0, "10秒前的秒级时间戳"),
        ]
        
        for data_timestamp, current_time_val, expected_age, description in test_cases:
            try:
                calculated_age = calculate_data_age(data_timestamp, current_time_val)
                age_diff = abs(calculated_age - expected_age)
                accuracy = age_diff < 0.1  # 100ms误差容忍
                
                self.log_finding(
                    "INFO" if accuracy else "HIGH",
                    f"数据年龄计算测试: {description}",
                    {
                        "data_timestamp": data_timestamp,
                        "current_time": current_time_val,
                        "expected_age": expected_age,
                        "calculated_age": calculated_age,
                        "age_difference": age_diff,
                        "accurate": accuracy
                    }
                )
                
                self.results["code_tests"].append({
                    "test": "calculate_data_age",
                    "data_timestamp": data_timestamp,
                    "calculated_age": calculated_age,
                    "expected_age": expected_age,
                    "accurate": accuracy,
                    "description": description
                })
                
            except Exception as e:
                self.log_finding("CRITICAL", f"数据年龄计算异常: {description}", {"error": str(e)})
    
    def analyze_log_timestamps(self):
        """分析日志中的时间戳模式"""
        print("\n🔍 分析日志中的时间戳模式...")
        
        log_file = Path("/root/myproject/123/66B 修复时间戳问题，但是全是错误/123/logs/websocket_performance_20250801.log")
        
        if not log_file.exists():
            self.log_finding("HIGH", "性能日志文件不存在", {"path": str(log_file)})
            return
        
        try:
            # 读取日志样本（前100行）
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = [f.readline().strip() for _ in range(100) if f.readline()]
            
            timestamp_diffs = []
            exchanges_timestamps = {"gate": [], "bybit": [], "okx": []}
            
            for line in lines:
                if "时间戳不同步" in line and "time_diff_ms" in line:
                    try:
                        # 提取时间差
                        if "'time_diff_ms': " in line:
                            start = line.find("'time_diff_ms': ") + len("'time_diff_ms': ")
                            end = line.find(",", start)
                            if end == -1:
                                end = line.find("}", start)
                            time_diff = float(line[start:end])
                            timestamp_diffs.append(time_diff)
                        
                        # 提取各交易所时间戳
                        if "'spot_timestamp': " in line:
                            start = line.find("'spot_timestamp': ") + len("'spot_timestamp': ")
                            end = line.find(",", start)
                            spot_ts = int(line[start:end])
                            
                            # 识别交易所
                            if "'spot_exchange': 'gate'" in line:
                                exchanges_timestamps["gate"].append(spot_ts)
                            elif "'spot_exchange': 'bybit'" in line:
                                exchanges_timestamps["bybit"].append(spot_ts)
                            elif "'spot_exchange': 'okx'" in line:
                                exchanges_timestamps["okx"].append(spot_ts)
                        
                        if "'futures_timestamp': " in line:
                            start = line.find("'futures_timestamp': ") + len("'futures_timestamp': ")
                            end = line.find("}", start)
                            futures_ts = int(line[start:end])
                            
                            # 识别交易所
                            if "'futures_exchange': 'gate'" in line:
                                exchanges_timestamps["gate"].append(futures_ts)
                            elif "'futures_exchange': 'bybit'" in line:
                                exchanges_timestamps["bybit"].append(futures_ts)
                            elif "'futures_exchange': 'okx'" in line:
                                exchanges_timestamps["okx"].append(futures_ts)
                                
                    except (ValueError, IndexError) as e:
                        continue
            
            # 分析时间差统计
            if timestamp_diffs:
                stats = {
                    "count": len(timestamp_diffs),
                    "max": max(timestamp_diffs),
                    "min": min(timestamp_diffs),
                    "mean": statistics.mean(timestamp_diffs),
                    "median": statistics.median(timestamp_diffs),
                    "over_1000ms": len([d for d in timestamp_diffs if d > 1000]),
                    "over_5000ms": len([d for d in timestamp_diffs if d > 5000]),
                    "over_9000ms": len([d for d in timestamp_diffs if d > 9000])
                }
                
                self.log_finding(
                    "CRITICAL" if stats["max"] > 9000 else "HIGH",
                    f"日志时间戳差异分析",
                    stats
                )
                
                self.results["timestamp_samples"]["time_differences"] = stats
            
            # 分析各交易所时间戳分布
            for exchange, timestamps in exchanges_timestamps.items():
                if timestamps:
                    # 转换为可读时间
                    readable_times = []
                    for ts in timestamps[:5]:  # 只分析前5个
                        try:
                            readable_time = datetime.fromtimestamp(ts / 1000).strftime('%H:%M:%S.%f')[:-3]
                            readable_times.append(readable_time)
                        except:
                            readable_times.append("invalid")
                    
                    self.results["timestamp_samples"][f"{exchange}_timestamps"] = {
                        "count": len(timestamps),
                        "sample_raw": timestamps[:5],
                        "sample_readable": readable_times,
                        "timestamp_range": max(timestamps) - min(timestamps) if len(timestamps) > 1 else 0
                    }
                    
                    self.log_finding(
                        "INFO",
                        f"{exchange.upper()} 时间戳样本分析",
                        {
                            "sample_count": len(timestamps),
                            "sample_timestamps": timestamps[:3],
                            "readable_times": readable_times[:3]
                        }
                    )
                        
        except Exception as e:
            self.log_finding("HIGH", f"日志分析异常: {str(e)}")
    
    def test_cross_exchange_sync(self):
        """测试跨交易所时间戳同步检查"""
        print("\n🔧 测试跨交易所时间戳同步检查...")
        
        try:
            processor = get_timestamp_processor("gate")
            
            current_time = int(time.time() * 1000)
            
            test_cases = [
                # (时间戳1, 时间戳2, 预期同步, 描述)
                (current_time, current_time + 100, True, "100ms差异 - 应该同步"),
                (current_time, current_time + 500, True, "500ms差异 - 应该同步"),
                (current_time, current_time + 900, False, "900ms差异 - 不应该同步"),
                (current_time, current_time + 5000, False, "5秒差异 - 严重不同步"),
                (current_time, current_time + 9500, False, "9.5秒差异 - 极端不同步")
            ]
            
            for ts1, ts2, expected_sync, description in test_cases:
                try:
                    is_synced, time_diff_ms = processor.validate_cross_exchange_sync(
                        ts1, ts2, "gate", "bybit", max_diff_ms=800
                    )
                    
                    result_correct = (is_synced == expected_sync)
                    
                    self.log_finding(
                        "INFO" if result_correct else "HIGH",
                        f"跨交易所同步测试: {description}",
                        {
                            "timestamp1": ts1,
                            "timestamp2": ts2,
                            "time_diff_ms": time_diff_ms,
                            "is_synced": is_synced,
                            "expected_sync": expected_sync,
                            "result_correct": result_correct
                        }
                    )
                    
                    self.results["code_tests"].append({
                        "test": "cross_exchange_sync",
                        "time_diff_ms": time_diff_ms,
                        "is_synced": is_synced,
                        "expected_sync": expected_sync,
                        "correct": result_correct,
                        "description": description
                    })
                    
                except Exception as e:
                    self.log_finding("CRITICAL", f"跨交易所同步测试异常: {description}", {"error": str(e)})
                    
        except Exception as e:
            self.log_finding("CRITICAL", f"跨交易所同步测试初始化失败: {str(e)}")
    
    async def test_network_latency(self):
        """测试网络延迟 - 模拟真实网络环境"""
        print("\n🌐 开始网络延迟测试...")
        
        import aiohttp
        
        # 三个交易所的时间API
        exchange_apis = {
            "gate": "https://api.gateio.ws/api/v4/spot/time",
            "bybit": "https://api.bybit.com/v5/market/time", 
            "okx": "https://www.okx.com/api/v5/public/time"
        }
        
        network_results = {}
        
        for exchange, api_url in exchange_apis.items():
            try:
                local_start = time.time() * 1000  # 本地时间戳(ms)
                
                async with aiohttp.ClientSession() as session:
                    start_time = time.time()
                    async with session.get(api_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        end_time = time.time()
                        
                        if response.status == 200:
                            data = await response.json()
                            network_latency = (end_time - start_time) * 1000  # 网络延迟(ms)
                            
                            # 提取服务器时间戳
                            server_timestamp = None
                            if exchange == "gate":
                                server_timestamp = data.get("server_time")
                            elif exchange == "bybit":
                                server_timestamp = int(data["result"]["timeNano"]) // 1000000
                            elif exchange == "okx":
                                server_timestamp = int(data["data"][0]["ts"])
                            
                            local_end = time.time() * 1000
                            
                            # 计算时间差异
                            if server_timestamp:
                                # 考虑网络延迟的时间差异
                                adjusted_local_time = local_start + (network_latency / 2)
                                time_difference = abs(server_timestamp - adjusted_local_time)
                                
                                network_results[exchange] = {
                                    "network_latency_ms": network_latency,
                                    "server_timestamp": server_timestamp,
                                    "local_timestamp": int(adjusted_local_time),
                                    "time_difference_ms": time_difference,
                                    "api_success": True
                                }
                                
                                # 判断时间差异是否异常
                                level = "INFO"
                                if time_difference > 5000:
                                    level = "CRITICAL"
                                elif time_difference > 1000:
                                    level = "HIGH"
                                
                                self.log_finding(
                                    level,
                                    f"{exchange.upper()} 网络时间同步测试",
                                    {
                                        "network_latency_ms": round(network_latency, 2),
                                        "time_difference_ms": round(time_difference, 2),
                                        "server_time": datetime.fromtimestamp(server_timestamp/1000).strftime('%H:%M:%S.%f')[:-3],
                                        "local_time": datetime.fromtimestamp(adjusted_local_time/1000).strftime('%H:%M:%S.%f')[:-3]
                                    }
                                )
                        else:
                            self.log_finding("HIGH", f"{exchange.upper()} API请求失败", {"status": response.status})
                            
            except asyncio.TimeoutError:
                self.log_finding("HIGH", f"{exchange.upper()} API请求超时")
                network_results[exchange] = {"api_success": False, "error": "timeout"}
            except Exception as e:
                self.log_finding("HIGH", f"{exchange.upper()} 网络测试异常", {"error": str(e)})
                network_results[exchange] = {"api_success": False, "error": str(e)}
        
        self.results["network_tests"] = network_results
        
        # 分析网络延迟是否是主要原因
        successful_tests = [r for r in network_results.values() if r.get("api_success")]
        if successful_tests:
            max_time_diff = max(r.get("time_difference_ms", 0) for r in successful_tests)
            avg_network_latency = statistics.mean(r.get("network_latency_ms", 0) for r in successful_tests)
            
            if max_time_diff > 5000:
                self.log_finding("CRITICAL", "发现网络时间同步问题", {
                    "max_time_difference_ms": max_time_diff,
                    "avg_network_latency_ms": avg_network_latency
                })
            else:
                self.log_finding("INFO", "网络时间同步正常", {
                    "max_time_difference_ms": max_time_diff,
                    "avg_network_latency_ms": avg_network_latency
                })
    
    def generate_conclusion(self):
        """生成诊断结论"""
        print("\n📊 生成诊断结论...")
        
        # 分析各类测试结果
        code_issues = len([f for f in self.results["findings"] if f["level"] in ["CRITICAL", "HIGH"] and "测试" in f["message"]])
        network_issues = len([f for f in self.results["findings"] if f["level"] in ["CRITICAL", "HIGH"] and "网络" in f["message"]])
        timestamp_issues = len([f for f in self.results["findings"] if f["level"] in ["CRITICAL", "HIGH"] and "时间戳" in f["message"]])
        
        # 从日志分析中获取最大时间差
        max_time_diff = 0
        if "timestamp_samples" in self.results and "time_differences" in self.results["timestamp_samples"]:
            max_time_diff = self.results["timestamp_samples"]["time_differences"].get("max", 0)
        
        # 生成结论
        if max_time_diff > 9000:
            if network_issues > code_issues:
                conclusion = f"🌐 **主要是网络问题**: 最大时间戳差异{max_time_diff:.0f}ms，主要由VPS网络延迟导致"
                primary_cause = "network"
            else:
                conclusion = f"🔧 **主要是代码问题**: 最大时间戳差异{max_time_diff:.0f}ms，时间戳处理逻辑存在缺陷"
                primary_cause = "code"
        elif max_time_diff > 5000:
            conclusion = f"⚠️ **混合问题**: 时间戳差异{max_time_diff:.0f}ms，网络和代码都有影响"
            primary_cause = "mixed"
        elif max_time_diff > 1000:
            conclusion = f"🔍 **轻微问题**: 时间戳差异{max_time_diff:.0f}ms，在可接受范围内但需优化"
            primary_cause = "minor"
        else:
            conclusion = f"✅ **正常范围**: 时间戳差异{max_time_diff:.0f}ms，系统运行正常"
            primary_cause = "normal"
        
        self.results["conclusion"] = conclusion
        self.results["primary_cause"] = primary_cause
        self.results["max_timestamp_difference_ms"] = max_time_diff
        
        print(f"\n🎯 **诊断结论**: {conclusion}")
        
        # 生成建议
        recommendations = []
        
        if primary_cause in ["code", "mixed"]:
            recommendations.extend([
                "检查OpportunityScanner中的时间戳单位混用问题",
                "验证统一时间戳处理器的ensure_milliseconds_timestamp函数",
                "检查跨交易所时间戳同步验证逻辑的阈值设置",
                "优化数据年龄计算函数的精度"
            ])
        
        if primary_cause in ["network", "mixed"]:
            recommendations.extend([
                "优化VPS网络配置，使用更快的DNS服务器",
                "考虑使用CDN或代理来改善与交易所的连接",
                "增加网络延迟补偿机制",
                "调整时间戳同步阈值以适应网络环境"
            ])
        
        if primary_cause == "normal":
            recommendations.extend([
                "继续监控时间戳差异，确保稳定性",
                "定期验证时间戳处理逻辑的准确性"
            ])
        
        self.results["recommendations"] = recommendations
        
        print(f"\n💡 **优化建议**:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始时间戳精确诊断...")
        print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 当前时间戳: {self.current_time_ms}")
        
        # 1. 测试时间戳处理函数
        self.test_timestamp_functions()
        
        # 2. 测试数据年龄计算
        self.test_data_age_calculation()
        
        # 3. 分析日志中的时间戳模式
        self.analyze_log_timestamps()
        
        # 4. 测试跨交易所同步检查
        self.test_cross_exchange_sync()
        
        # 5. 测试网络延迟
        await self.test_network_latency()
        
        # 6. 生成结论和建议
        self.generate_conclusion()
        
        # 7. 保存诊断结果
        self.save_results()
        
        print(f"\n✅ 诊断完成!")
        return self.results
    
    def save_results(self):
        """保存诊断结果"""
        output_file = f"/root/myproject/123/66B 修复时间戳问题，但是全是错误/123/diagnostic_results/timestamp_diagnosis_{int(time.time())}.json"
        
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"📄 诊断结果已保存: {output_file}")
            
        except Exception as e:
            print(f"❌ 保存诊断结果失败: {e}")

async def main():
    """主函数"""
    tool = TimestampDiagnosticTool()
    await tool.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())