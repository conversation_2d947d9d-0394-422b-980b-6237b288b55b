{"timestamp": "2025-08-01T16:36:24.569428", "diagnostic_type": "timestamp_precision_analysis", "findings": [{"timestamp": "2025-08-01T16:36:24.569480", "level": "INFO", "message": "时间戳标准化测试: 标准毫秒级时间戳", "data": {"input": 1754058178612, "output": 1754058178612, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569498", "level": "INFO", "message": "时间戳标准化测试: 浮点数毫秒级时间戳", "data": {"input": 1754058178.612, "output": 1754058178612, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569514", "level": "INFO", "message": "时间戳标准化测试: 秒级时间戳", "data": {"input": 1754058178, "output": 1754058178000, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569523", "level": "INFO", "message": "时间戳标准化测试: 浮点数秒级时间戳", "data": {"input": 1754058178.0, "output": 1754058178000, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569533", "level": "INFO", "message": "时间戳标准化测试: None值处理", "data": {"input": null, "output": 1754058984569, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569542", "level": "INFO", "message": "时间戳标准化测试: 零值处理", "data": {"input": 0, "output": 1754058984569, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569550", "level": "INFO", "message": "时间戳标准化测试: 负值处理", "data": {"input": -1, "output": 1754058984569, "output_type": "int", "expected_type": "int", "success": true}}, {"timestamp": "2025-08-01T16:36:24.569588", "level": "INFO", "message": "数据年龄计算测试: 1秒前的毫秒级时间戳", "data": {"data_timestamp": 1754058983569, "current_time": 1754058984.5695808, "expected_age": 1.0, "calculated_age": 1.0, "age_difference": 0.0, "accurate": true}}, {"timestamp": "2025-08-01T16:36:24.569601", "level": "INFO", "message": "数据年龄计算测试: 500ms前的毫秒级时间戳", "data": {"data_timestamp": 1754058984069, "current_time": 1754058984.5695808, "expected_age": 0.5, "calculated_age": 0.5, "age_difference": 0.0, "accurate": true}}, {"timestamp": "2025-08-01T16:36:24.569611", "level": "HIGH", "message": "数据年龄计算测试: 1秒前的秒级时间戳", "data": {"data_timestamp": 1754058983, "current_time": 1754058984.5695808, "expected_age": 1.0, "calculated_age": 1.569, "age_difference": 0.569, "accurate": false}}, {"timestamp": "2025-08-01T16:36:24.569627", "level": "HIGH", "message": "数据年龄计算测试: 10秒前的秒级时间戳", "data": {"data_timestamp": 1754058974, "current_time": 1754058984.5695808, "expected_age": 10.0, "calculated_age": 10.569, "age_difference": 0.5690000000000008, "accurate": false}}, {"timestamp": "2025-08-01T16:36:24.570256", "level": "CRITICAL", "message": "日志时间戳差异分析", "data": {"count": 50, "max": 9714.0, "min": 994.0, "mean": 5903.92, "median": 7294.0, "over_1000ms": 45, "over_5000ms": 31, "over_9000ms": 7}}, {"timestamp": "2025-08-01T16:36:24.570326", "level": "INFO", "message": "GATE 时间戳样本分析", "data": {"sample_count": 45, "sample_timestamps": [1754058175739, 1754058175739, 1754058170734], "readable_times": ["16:22:55.739", "16:22:55.739", "16:22:50.734"]}}, {"timestamp": "2025-08-01T16:36:24.570370", "level": "INFO", "message": "BYBIT 时间戳样本分析", "data": {"sample_count": 29, "sample_timestamps": [1754058178482, 1754058176128, 1754058178481], "readable_times": ["16:22:58.482", "16:22:56.128", "16:22:58.481"]}}, {"timestamp": "2025-08-01T16:36:24.570409", "level": "INFO", "message": "OKX 时间戳样本分析", "data": {"sample_count": 26, "sample_timestamps": [1754058178517, 1754058178611, 1754058178519], "readable_times": ["16:22:58.517", "16:22:58.611", "16:22:58.519"]}}, {"timestamp": "2025-08-01T16:36:24.570462", "level": "INFO", "message": "跨交易所同步测试: 100ms差异 - 应该同步", "data": {"timestamp1": 1754058984570, "timestamp2": 1754058984670, "time_diff_ms": 100, "is_synced": true, "expected_sync": true, "result_correct": true}}, {"timestamp": "2025-08-01T16:36:24.570479", "level": "INFO", "message": "跨交易所同步测试: 500ms差异 - 应该同步", "data": {"timestamp1": 1754058984570, "timestamp2": 1754058985070, "time_diff_ms": 500, "is_synced": true, "expected_sync": true, "result_correct": true}}, {"timestamp": "2025-08-01T16:36:24.570511", "level": "INFO", "message": "跨交易所同步测试: 900ms差异 - 不应该同步", "data": {"timestamp1": 1754058984570, "timestamp2": 1754058985470, "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "result_correct": true}}, {"timestamp": "2025-08-01T16:36:24.570529", "level": "INFO", "message": "跨交易所同步测试: 5秒差异 - 严重不同步", "data": {"timestamp1": 1754058984570, "timestamp2": 1754058989570, "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "result_correct": true}}, {"timestamp": "2025-08-01T16:36:24.570553", "level": "INFO", "message": "跨交易所同步测试: 9.5秒差异 - 极端不同步", "data": {"timestamp1": 1754058984570, "timestamp2": 1754058994070, "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "result_correct": true}}, {"timestamp": "2025-08-01T16:36:24.781524", "level": "INFO", "message": "GATE 网络时间同步测试", "data": {"network_latency_ms": 27.28, "time_difference_ms": 10.74, "server_time": "16:36:24.778", "local_time": "16:36:24.767"}}, {"timestamp": "2025-08-01T16:36:24.879141", "level": "INFO", "message": "BYBIT 网络时间同步测试", "data": {"network_latency_ms": 95.96, "time_difference_ms": 11.43, "server_time": "16:36:24.842", "local_time": "16:36:24.830"}}, {"timestamp": "2025-08-01T16:36:24.973333", "level": "INFO", "message": "OKX 网络时间同步测试", "data": {"network_latency_ms": 93.23, "time_difference_ms": 16.77, "server_time": "16:36:24.943", "local_time": "16:36:24.926"}}, {"timestamp": "2025-08-01T16:36:24.973815", "level": "INFO", "message": "网络时间同步正常", "data": {"max_time_difference_ms": 16.76513671875, "avg_network_latency_ms": 72.15992609659831}}], "network_tests": {"gate": {"network_latency_ms": 27.284860610961914, "server_timestamp": 1754058984778, "local_timestamp": 1754058984767, "time_difference_ms": 10.736572265625, "api_success": true}, "bybit": {"network_latency_ms": 95.96371650695801, "server_timestamp": 1754058984842, "local_timestamp": 1754058984830, "time_difference_ms": 11.428955078125, "api_success": true}, "okx": {"network_latency_ms": 93.231201171875, "server_timestamp": 1754058984943, "local_timestamp": 1754058984926, "time_difference_ms": 16.76513671875, "api_success": true}}, "code_tests": [{"test": "ensure_milliseconds_timestamp", "input": 1754058178612, "output": 1754058178612, "success": true, "description": "标准毫秒级时间戳"}, {"test": "ensure_milliseconds_timestamp", "input": 1754058178.612, "output": 1754058178612, "success": true, "description": "浮点数毫秒级时间戳"}, {"test": "ensure_milliseconds_timestamp", "input": 1754058178, "output": 1754058178000, "success": true, "description": "秒级时间戳"}, {"test": "ensure_milliseconds_timestamp", "input": 1754058178.0, "output": 1754058178000, "success": true, "description": "浮点数秒级时间戳"}, {"test": "ensure_milliseconds_timestamp", "input": null, "output": 1754058984569, "success": true, "description": "None值处理"}, {"test": "ensure_milliseconds_timestamp", "input": 0, "output": 1754058984569, "success": true, "description": "零值处理"}, {"test": "ensure_milliseconds_timestamp", "input": -1, "output": 1754058984569, "success": true, "description": "负值处理"}, {"test": "calculate_data_age", "data_timestamp": 1754058983569, "calculated_age": 1.0, "expected_age": 1.0, "accurate": true, "description": "1秒前的毫秒级时间戳"}, {"test": "calculate_data_age", "data_timestamp": 1754058984069, "calculated_age": 0.5, "expected_age": 0.5, "accurate": true, "description": "500ms前的毫秒级时间戳"}, {"test": "calculate_data_age", "data_timestamp": 1754058983, "calculated_age": 1.569, "expected_age": 1.0, "accurate": false, "description": "1秒前的秒级时间戳"}, {"test": "calculate_data_age", "data_timestamp": 1754058974, "calculated_age": 10.569, "expected_age": 10.0, "accurate": false, "description": "10秒前的秒级时间戳"}, {"test": "cross_exchange_sync", "time_diff_ms": 100, "is_synced": true, "expected_sync": true, "correct": true, "description": "100ms差异 - 应该同步"}, {"test": "cross_exchange_sync", "time_diff_ms": 500, "is_synced": true, "expected_sync": true, "correct": true, "description": "500ms差异 - 应该同步"}, {"test": "cross_exchange_sync", "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "correct": true, "description": "900ms差异 - 不应该同步"}, {"test": "cross_exchange_sync", "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "correct": true, "description": "5秒差异 - 严重不同步"}, {"test": "cross_exchange_sync", "time_diff_ms": Infinity, "is_synced": false, "expected_sync": false, "correct": true, "description": "9.5秒差异 - 极端不同步"}], "timestamp_samples": {"time_differences": {"count": 50, "max": 9714.0, "min": 994.0, "mean": 5903.92, "median": 7294.0, "over_1000ms": 45, "over_5000ms": 31, "over_9000ms": 7}, "gate_timestamps": {"count": 45, "sample_raw": [1754058175739, 1754058175739, 1754058170734, 1754058168801, 1754058176664], "sample_readable": ["16:22:55.739", "16:22:55.739", "16:22:50.734", "16:22:48.801", "16:22:56.664"], "timestamp_range": 8810}, "bybit_timestamps": {"count": 29, "sample_raw": [1754058178482, 1754058176128, 1754058178481, 1754058178029, 1754058178028], "sample_readable": ["16:22:58.482", "16:22:56.128", "16:22:58.481", "16:22:58.029", "16:22:58.028"], "timestamp_range": 2354}, "okx_timestamps": {"count": 26, "sample_raw": [1754058178517, 1754058178611, 1754058178519, 1754058178611, 1754058178605], "sample_readable": ["16:22:58.517", "16:22:58.611", "16:22:58.519", "16:22:58.611", "16:22:58.605"], "timestamp_range": 97}}, "conclusion": "🔧 **主要是代码问题**: 最大时间戳差异9714ms，时间戳处理逻辑存在缺陷", "recommendations": ["检查OpportunityScanner中的时间戳单位混用问题", "验证统一时间戳处理器的ensure_milliseconds_timestamp函数", "检查跨交易所时间戳同步验证逻辑的阈值设置", "优化数据年龄计算函数的精度"], "primary_cause": "code", "max_timestamp_difference_ms": 9714.0}