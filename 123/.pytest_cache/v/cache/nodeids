["tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_concurrent_reconnection_pressure", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_failure_recovery_resilience", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_health_monitoring_integration", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_multiple_exchange_consistency", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_no_duplicate_reconnection_logic", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_root_cause_analysis_capability", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_root_cause_fixing_capability", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_unified_interface_consistency", "tests/test_unified_reconnection_mechanism.py::TestUnifiedReconnectionMechanism::test_websocket_to_pool_manager_integration"]